use crate::models::config::MonitorConfig;
use crate::services::{
    account::AccountService, auth::AuthService, config::ConfigService, monitor::MonitorService,
    notification::NotificationService,
};
use crate::utils::{WindowConfig, WindowFactory, WindowParams};
use log::{info, warn};
use std::future::Future;
use std::pin::Pin;
use std::sync::Arc;
use tauri::AppHandle;
use uuid;

/// Cookie操作回调类型
pub type CookieCallback =
    Box<dyn Fn(String) -> Pin<Box<dyn Future<Output = Result<(), String>> + Send>> + Send + Sync>;

/// 闲鱼业务逻辑处理器 - 协调各个服务完成业务功能
#[derive(Clone)]
pub struct GoldfishBusiness {
    app_handle: AppHandle,
    config_service: ConfigService,
    auth_service: AuthService,
    monitor_service: MonitorService,
    notification_service: NotificationService,
    account_service: AccountService,
}

impl GoldfishBusiness {
    /// 创建新的业务处理器
    pub fn new(
        app_handle: AppHandle,
        config_service: ConfigService,
        auth_service: AuthService,
        monitor_service: MonitorService,
        notification_service: NotificationService,
        account_service: AccountService,
    ) -> Self {
        Self {
            app_handle,
            config_service,
            auth_service,
            monitor_service,
            notification_service,
            account_service,
        }
    }

    /// 启动闲鱼监控业务流程
    pub async fn start_goldfish_monitoring(&self) -> Result<(), String> {
        // 1. 记录业务日志
        info!("GoldfishBusiness::start_goldfish_monitoring - 开始启动闲鱼监控");

        // 2. 检查激活状态
        if !self.auth_service.check_activation_status().await? {
            return Err("应用未激活，无法启动监控".to_string());
        }

        // 3. 验证所有启用账号的Cookie有效性
        info!("GoldfishBusiness::start_goldfish_monitoring - 开始验证账号Cookie有效性");

        if let Err(e) = self.account_service.validate_all_accounts().await {
            return Err(format!("账号验证失败: {}", e));
        }

        // 4. 获取配置
        let config = self.config_service.get_config().await;

        // 5. 设置通知渠道
        self.setup_notification_channels(&config).await?;

        // 6. 启动监控
        self.monitor_service.start_monitoring(config).await?;

        // 7. 记录成功日志
        info!("GoldfishBusiness::start_goldfish_monitoring - 闲鱼监控启动成功");

        Ok(())
    }

    /// 停止闲鱼监控
    pub async fn stop_goldfish_monitoring(&self) -> Result<(), String> {
        info!("GoldfishBusiness::stop_goldfish_monitoring - 停止闲鱼监控");

        self.monitor_service.stop_monitoring().await?;

        Ok(())
    }

    /// 统一的Cookie获取方法 - 通过打开浏览器窗口获取Cookie
    ///
    /// # 参数
    /// - `account_info`: 账号信息（为空表示新登录，存在表示更新或验证）
    /// - `callback`: 可选的自定义回调函数
    /// - `url`: 可选的自定义URL，默认使用闲鱼搜索页
    pub async fn get_cookies_by_open_browser(
        &self,
        account_info: Option<&crate::models::account::Account>,
        callback: Option<CookieCallback>,
        url: Option<String>,
    ) -> Result<(), String> {
        let default_url = "https://www.goofish.com/search?q=相机";
        let target_url = url.as_deref().unwrap_or(default_url);

        // 根据账号信息确定操作类型和窗口标题
        let (window_title, window_label) = if let Some(account) = account_info {
            (
                format!("账号操作 - {} - 请完成操作后关闭窗口", account.name),
                format!("goofish_operation_{}", uuid::Uuid::new_v4()),
            )
        } else {
            (
                "闲鱼登录 - 请完成登录后关闭窗口".to_string(),
                format!("goofish_login_{}", uuid::Uuid::new_v4()),
            )
        };

        println!("🔄 开始统一Cookie操作，目标URL: {}", target_url);

        // 会话准备
        let session_id = self
            .prepare_session_for_unified_operation(account_info)
            .await?;

        info!("🔄 会话准备完成: session_id={}", session_id);

        // 创建窗口参数
        let self_clone = self.clone();
        let session_id_clone = session_id.clone();
        let account_info_clone = account_info.cloned();
        let callback_arc = callback.map(Arc::new);

        let params = WindowParams::new()
            .with_title(&window_title)
            .with_config(WindowConfig::default().with_size(1200.0, 800.0))
            .with_callback(move |window, event| {
                let self_clone = self_clone.clone();
                let session_id_clone = session_id_clone.clone();
                let account_info_clone = account_info_clone.clone();
                let callback_arc = callback_arc.clone();
                let window = window.clone();
                match event {
                    tauri::WindowEvent::CloseRequested { .. } => {
                        println!("🔄 统一Cookie操作窗口关闭请求，开始同步提取Cookie");
                        println!("🔄====================Cookie操作窗口关闭请求，开始同步提取Cookie===================={}", window.label());

                        // 同步提取Cookie，不阻塞窗口关闭
                        if let Ok(cookies) = window.cookies() {
                            println!("🍪 成功提取了 {} 个Cookie", cookies.len());
                            info!("🔍 窗口标签: {}, 会话ID: {}, 账号信息: {:?}",
                                    window.label(), session_id_clone,
                                    account_info_clone.as_ref().map(|a| format!("{}({})", a.name, a.id)));

                            // 同步过滤和处理Cookie
                            let cookie_string = self_clone.extract_and_filter_cookies(cookies);

                            if !cookie_string.is_empty() {
                                info!("🔄 Cookie提取成功，开始业务处理，Cookie长度: {}", cookie_string.len());

                                // 优化处理逻辑：根据callback存在与否选择处理方式
                                if let Some(callback_arc) = callback_arc.as_ref() {
                                    // 使用自定义回调 - 异步处理
                                    let callback_arc_async = callback_arc.clone();
                                    let cookie_string_async = cookie_string.clone();

                                    tauri::async_runtime::spawn(async move {
                                        match callback_arc_async(cookie_string_async).await {
                                            Ok(_) => println!("✅ 自定义Cookie回调成功"),
                                            Err(e) => println!("❌ 自定义Cookie回调失败: {}", e),
                                        }
                                    });
                                } else {
                                    // 使用默认回调逻辑 - 在新线程中处理
                                    let self_clone_sync = self_clone.clone();
                                    let session_id_sync = session_id_clone.clone();
                                    let account_info_sync = account_info_clone.clone();
                                    let cookie_string_sync = cookie_string.clone();

                                    std::thread::spawn(move || {
                                        // 使用 tokio 运行时来处理异步方法，添加错误处理
                                        match tokio::runtime::Runtime::new() {
                                            Ok(rt) => {
                                                match rt.block_on(self_clone_sync.handle_default_cookie_callback(
                                                    cookie_string_sync,
                                                    account_info_sync.as_ref(),
                                                    &session_id_sync,
                                                    &window.label(),
                                                )) {
                                                    Ok(_) => println!("✅ 默认Cookie回调成功"),
                                                    Err(e) => println!("❌ 默认Cookie回调失败: {}", e),
                                                }
                                            }
                                            Err(e) => {
                                                println!("❌ 创建Tokio运行时失败: {}", e);
                                            }
                                        }
                                    });
                                }
                            } else {
                                println!("⚠️ 未找到有效的Cookie");
                            }
                        } else {
                            println!("⚠️ Cookie提取失败");
                        }

                        // 窗口会自然关闭，不需要手动调用 close()
                        println!("✅ Cookie提取完成，窗口将自然关闭");
                    }
                    _ => {}
                }
            });

        // 创建窗口 - 这里是唯一需要区分平台的地方
        let window = WindowFactory::create_window_with_label(
            &self.app_handle,
            &window_label,
            target_url,
            Some(params),
            Some(&session_id),
        )?;

        info!(
            "GoldfishBusiness::get_cookies_by_open_browser - 已打开窗口: {} (会话ID: {})",
            window.label(),
            session_id
        );

        Ok(())
    }

    /// 为统一操作准备会话
    async fn prepare_session_for_unified_operation(
        &self,
        account_info: Option<&crate::models::account::Account>,
    ) -> Result<String, String> {
        match account_info {
            None => {
                // 新登录，生成会话ID
                let session_id = uuid::Uuid::new_v4().to_string();
                info!("🆕 新登录会话: session_id={}", session_id);
                Ok(session_id)
            }
            Some(account) => {
                // 更新或验证现有账号，使用标准会话
                info!(
                    "🔄 现有账号操作: account_id={}, name={}",
                    account.id, account.name
                );
                let session_id = self
                    .account_service
                    .reuse_login_session_for_account(&account.id)
                    .await?;
                info!("🔄 重用会话: session_id={}", session_id);
                Ok(session_id)
            }
        }
    }

    /// 默认Cookie回调处理
    async fn handle_default_cookie_callback(
        &self,
        cookie_string: String,
        account_info: Option<&crate::models::account::Account>,
        session_id: &str,
        window_label: &str,
    ) -> Result<(), String> {
        info!(
            "🔄 默认Cookie回调开始: session_id={}, cookie_length={}, account_info={:?}",
            session_id,
            cookie_string.len(),
            account_info.map(|a| format!("{}({})", a.name, a.id))
        );
        match account_info {
            None => {
                // 新登录：验证Cookie并自动添加账号
                println!("🔄 处理新登录Cookie");
                self.handle_login_window_close_with_cookie(session_id, cookie_string)
                    .await
            }
            Some(account) => {
                // 更新或验证现有账号
                println!("🔄 处理账号更新/验证Cookie: {}", account.name);

                // 使用标准处理
                match self
                    .account_service
                    .handle_login_session_completed_with_cookie(session_id, cookie_string)
                    .await
                {
                    Ok(_) => {
                        println!("✅ 账号Cookie更新成功");

                        // 检查窗口标签来判断是验证操作还是更新操作
                        if window_label.contains("goofish_operation_") {
                            // 这是验证操作，需要恢复监控并可能自动启动
                            println!("🔐 这是验证操作，处理验证完成");
                            self.handle_account_verification_completed(&account.id)
                                .await
                        } else {
                            // 这是更新操作，只更新Cookie不自动启动监控
                            println!("🔄 这是更新操作，只更新Cookie");
                            self.handle_account_cookie_updated(&account.id).await
                        }
                    }
                    Err(e) => Err(format!("账号Cookie处理失败: {}", e)),
                }
            }
        }
    }

    /// 清理统一会话
    async fn cleanup_unified_session(
        &self,
        session_id: &str,
        account_info: Option<&crate::models::account::Account>,
    ) -> Result<(), String> {
        match account_info {
            None => {
                // 新登录会话清理
                self.account_service.cleanup_login_session(session_id).await
            }
            Some(_) => {
                // 更新/验证会话保留以便重用
                Ok(())
            }
        }
    }

    /// 处理闲鱼登录流程
    pub async fn handle_goldfish_login(&self) -> Result<(), String> {
        info!("GoldfishBusiness::handle_goldfish_login - 开始闲鱼登录流程");

        // 使用统一的Cookie操作方法
        self.get_cookies_by_open_browser(None, None, None).await
    }

    /// 处理标准登录窗口的Cookie（新方法）
    async fn handle_login_window_close_with_cookie(
        &self,
        session_id: &str,
        cookie_string: String,
    ) -> Result<(), String> {
        println!("🔄 开始验证Cookie并自动添加账号");

        // 验证Cookie并获取用户信息
        match self.account_service.get_user_info(&cookie_string).await {
            Ok(user_info) if user_info.is_valid => {
                println!("✅ Cookie验证成功，自动添加账号");

                // 添加账号
                match self
                    .account_service
                    .add_account_with_user_info(
                        user_info
                            .nickname
                            .clone()
                            .unwrap_or_else(|| "未知用户".to_string()),
                        user_info.user_id.clone(),
                        cookie_string,
                        Some("通过登录窗口自动添加".to_string()),
                    )
                    .await
                {
                    Ok((account, is_update)) => {
                        info!(
                            "🔍 账号操作结果: account_id={}, name={}, is_update={}",
                            account.id, account.name, is_update
                        );

                        // 保存session_id到账号metadata（确保后续更新时能重用相同的identity）
                        info!(
                            "💾 准备保存会话ID: session_id={} -> account_id={}",
                            session_id, account.id
                        );
                        if let Err(e) = self
                            .account_service
                            .save_session_id_to_account(&account.id, session_id)
                            .await
                        {
                            warn!("⚠️ 保存会话ID失败: {}", e);
                        } else {
                            info!(
                                "✅ 已保存会话ID到账号: session_id={}, account_id={}",
                                session_id, account.id
                            );
                        }

                        let message = if is_update {
                            format!("账号 {} 信息已更新", account.name)
                        } else {
                            format!("账号 {} 已添加", account.name)
                        };
                        println!("✅ {}", message);
                    }
                    Err(e) => {
                        println!("⚠️ 账号添加失败: {}", e);
                    }
                }
            }
            Ok(_) => {
                println!("⚠️ Cookie验证失败");
            }
            Err(e) => {
                println!("⚠️ Cookie验证出错: {}", e);
            }
        }

        Ok(())
    }

    /// 设置通知渠道
    async fn setup_notification_channels(&self, config: &MonitorConfig) -> Result<(), String> {
        // 设置钉钉通知渠道（仅在开关开启时）
        if config.dingtalk_enabled {
            for (index, hook_url) in config.dingtalk_hooks.iter().enumerate() {
                let channel_name = format!("dingtalk_{}", index);
                let channel = crate::services::notification::NotificationChannel::DingTalk {
                    webhook_url: hook_url.clone(),
                };

                self.notification_service
                    .add_channel(channel_name, channel)
                    .await?;
            }
        }

        // 添加系统通知渠道
        let system_channel = crate::services::notification::NotificationChannel::System;
        self.notification_service
            .add_channel("system".to_string(), system_channel)
            .await?;

        Ok(())
    }

    /// 发送新商品通知
    pub async fn send_new_item_notification(
        &self,
        item: &crate::models::config::MonitorItem,
    ) -> Result<(), String> {
        let message = crate::services::notification::NotificationMessage {
            title: "发现新商品".to_string(),
            content: format!(
                "商品: {}\n价格: {}\n卖家: {}",
                item.title, item.price, item.user_nick_name
            ),
            level: crate::services::notification::service::NotificationLevel::Info,
            timestamp: chrono::Utc::now().to_rfc3339(),
            metadata: std::collections::HashMap::new(),
        };

        self.notification_service
            .broadcast_notification(message)
            .await?;

        Ok(())
    }

    /// 获取业务状态
    pub async fn get_business_status(&self) -> Result<serde_json::Value, String> {
        let monitor_status = self.monitor_service.get_status().await;
        let activation_info = self.auth_service.get_activation_info().await;
        let config = self.config_service.get_config().await;

        Ok(serde_json::json!({
            "monitor": monitor_status,
            "activation": activation_info,
            "has_cookie": config.has_cookie(),
            "timestamp": chrono::Utc::now().to_rfc3339()
        }))
    }

    /// 处理带ID的闲鱼登录流程（用于自动添加账号）
    pub async fn handle_goldfish_login_with_id(
        &self,
        login_id: &str,
        description: Option<String>,
    ) -> Result<(), String> {
        info!(
            "GoldfishBusiness::handle_goldfish_login_with_id - 开始带ID的闲鱼登录流程: login_id={}",
            login_id
        );

        // 创建自定义回调来处理带ID的登录
        let self_clone = self.clone();
        let description_clone = description.clone();
        let callback: CookieCallback = Box::new(move |cookie_string: String| {
            let self_clone = self_clone.clone();
            let description_clone = description_clone.clone();

            Box::pin(async move {
                println!("� 带ID登录回调：处理Cookie并添加账号");

                // 验证Cookie并获取用户信息
                match self_clone
                    .account_service
                    .get_user_info(&cookie_string)
                    .await
                {
                    Ok(user_info) if user_info.is_valid => {
                        println!("✅ Cookie验证成功，自动添加账号");

                        // 添加账号
                        match self_clone
                            .account_service
                            .add_account_with_user_info(
                                user_info
                                    .nickname
                                    .clone()
                                    .unwrap_or_else(|| "未知用户".to_string()),
                                user_info.user_id.clone(),
                                cookie_string,
                                description_clone
                                    .or_else(|| Some("通过登录窗口自动添加".to_string())),
                            )
                            .await
                        {
                            Ok((account, is_update)) => {
                                let message = if is_update {
                                    format!("账号 {} 信息已更新", account.name)
                                } else {
                                    format!("账号 {} 已添加", account.name)
                                };
                                println!("✅ {}", message);
                                Ok(())
                            }
                            Err(e) => {
                                println!("⚠️ 账号添加失败: {}", e);
                                Err(format!("账号添加失败: {}", e))
                            }
                        }
                    }
                    Ok(_) => {
                        println!("⚠️ Cookie验证失败");
                        Err("Cookie验证失败".to_string())
                    }
                    Err(e) => {
                        println!("⚠️ Cookie验证出错: {}", e);
                        Err(format!("Cookie验证出错: {}", e))
                    }
                }
            })
        });

        // 使用统一的Cookie操作方法
        self.get_cookies_by_open_browser(None, Some(callback), None)
            .await
    }

    /// 更新指定账号的Cookie
    pub async fn update_account_cookie(&self, account_id: &str) -> Result<(), String> {
        info!(
            "GoldfishBusiness::update_account_cookie - 开始更新账号Cookie: {}",
            account_id
        );

        // 获取账号信息
        let account = self
            .account_service
            .get_account(account_id)
            .await
            .ok_or_else(|| format!("账号不存在: {}", account_id))?;

        // 使用统一的Cookie操作方法，传入账号信息表示这是更新操作
        self.get_cookies_by_open_browser(Some(&account), None, None)
            .await
    }

    /// 提取和过滤Cookie
    fn extract_and_filter_cookies(&self, cookies: Vec<tauri::webview::Cookie>) -> String {
        // 查找闲鱼相关的Cookie
        let mut goofish_cookies = Vec::new();
        let mut all_cookies = Vec::new();

        for cookie in cookies {
            let cookie_str = format!("{}={}", cookie.name(), cookie.value());
            all_cookies.push(cookie_str.clone());

            if let Some(domain) = cookie.domain() {
                if domain.contains("goofish.com") || domain.contains("taobao.com") {
                    goofish_cookies.push(cookie_str);
                }
            }
        }

        if !goofish_cookies.is_empty() {
            // 构建闲鱼相关Cookie字符串
            goofish_cookies.join("; ")
        } else {
            // 如果没有找到闲鱼相关Cookie，返回所有Cookie
            all_cookies.join("; ")
        }
    }

    /// 处理账号验证请求（打开验证窗口）
    pub async fn handle_account_verification(&self, account_id: &str) -> Result<(), String> {
        println!("🔐 处理账号验证请求: account_id={}", account_id);

        info!(
            "GoldfishBusiness::handle_account_verification - 开始处理账号验证: account_id={}",
            account_id
        );

        // 获取账号信息
        let account = {
            let config = self.account_service.get_config().await;
            config
                .accounts
                .iter()
                .find(|a| a.id == account_id)
                .cloned()
                .ok_or_else(|| format!("账号不存在: {}", account_id))?
        };

        // 使用统一的Cookie操作方法，传入账号信息表示这是验证操作
        self.get_cookies_by_open_browser(Some(&account), None, None)
            .await
    }

    /// 处理账号Cookie更新完成（不自动启动监控）
    pub async fn handle_account_cookie_updated(&self, account_id: &str) -> Result<(), String> {
        println!("✅ 处理账号Cookie更新完成: account_id={}", account_id);

        info!(
            "GoldfishBusiness::handle_account_cookie_updated - 账号Cookie更新完成: account_id={}",
            account_id
        );

        // 验证Cookie有效性
        let is_valid = self
            .account_service
            .validate_account_cookie(account_id)
            .await?;

        if is_valid {
            info!(
                "GoldfishBusiness::handle_account_cookie_updated - 账号 {} Cookie验证成功",
                account_id
            );
            println!("✅ 账号 {} Cookie更新成功", account_id);
        } else {
            log::warn!(
                "GoldfishBusiness::handle_account_cookie_updated - 账号 {} Cookie验证失败",
                account_id
            );
            println!("❌ 账号 {} Cookie验证失败", account_id);
        }

        Ok(())
    }

    /// 处理账号验证完成（验证码验证后自动启动监控）
    pub async fn handle_account_verification_completed(
        &self,
        account_id: &str,
    ) -> Result<(), String> {
        println!("✅ 处理账号验证完成: account_id={}", account_id);

        info!(
            "GoldfishBusiness::handle_account_verification_completed - 账号验证完成: account_id={}",
            account_id
        );

        // 由于验证窗口已经自动更新了Cookie，这里直接验证Cookie有效性
        let is_valid = self
            .account_service
            .validate_account_cookie(account_id)
            .await?;

        if is_valid {
            // Cookie有效，恢复监控
            self.monitor_service
                .resume_account_after_verification(account_id)
                .await?;

            // 检查监控是否已停止，如果停止则自动启动
            if !self.monitor_service.is_running().await {
                info!("GoldfishBusiness::handle_account_verification_completed - 监控已停止，验证完成后自动启动监控");

                // 获取配置并启动监控
                let config = self.config_service.get_config().await;
                if let Err(e) = self.monitor_service.start_monitoring(config).await {
                    log::warn!("GoldfishBusiness::handle_account_verification_completed - 自动启动监控失败: {}", e);
                    println!("⚠️ 自动启动监控失败: {}", e);
                } else {
                    println!("🚀 验证完成，监控已自动启动");
                }
            }

            info!("GoldfishBusiness::handle_account_verification_completed - 账号 {} Cookie验证成功，已恢复监控", account_id);

            println!("✅ 账号 {} 验证成功，监控已恢复", account_id);
        } else {
            log::warn!("GoldfishBusiness::handle_account_verification_completed - 账号 {} Cookie验证失败，保持暂停状态", account_id);

            println!("❌ 账号 {} Cookie验证失败，保持暂停状态", account_id);
        }

        Ok(())
    }
}
